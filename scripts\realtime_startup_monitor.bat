@echo off
REM Real-time startup performance monitor for TJ_BatteryOne
REM Monitors specific performance tags during app startup

setlocal enabledelayedexpansion

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set MAIN_ACTIVITY=%APP_ID%/.activity.splash.SplashActivity

echo ========================================
echo TJ_BatteryOne Real-time Startup Monitor
echo ========================================
echo Application ID: %APP_ID%
echo.

REM Check if ADB is available
if not exist "%ADB_PATH%" (
    echo ERROR: ADB not found at %ADB_PATH%
    pause
    exit /b 1
)

echo Preparing for startup monitoring...
echo.
echo 1. Force stopping app...
"%ADB_PATH%" shell am force-stop %APP_ID%
timeout /t 2 /nobreak > nul

echo 2. Clearing logcat buffer...
"%ADB_PATH%" logcat -c

echo 3. Starting real-time monitoring...
echo    - STARTUP_TIMING: Overall app startup metrics
echo    - MAX_INIT: MAX SDK initialization progress  
echo    - AD_ADAPTER_LOAD: Ad adapter loading times
echo    - SPLASH_PROGRESS: Splash screen progression
echo    - Service startup logs
echo.
echo Press Ctrl+C to stop monitoring
echo ========================================

REM Start the app and monitor in real-time
echo Launching app...
start /b "%ADB_PATH%" shell am start -W -n %MAIN_ACTIVITY%

REM Monitor logcat with relevant tags
"%ADB_PATH%" logcat -s STARTUP_TIMING:D MAX_INIT:D AD_ADAPTER_LOAD:D SPLASH_PROGRESS:D BatteryApplication:D MainActivity:D SplashActivity:D CoreBatteryStatsService:D InitializationProgressManager:D

pause
