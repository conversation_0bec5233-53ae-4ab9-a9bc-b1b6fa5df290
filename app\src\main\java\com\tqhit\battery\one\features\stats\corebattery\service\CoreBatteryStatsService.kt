package com.tqhit.battery.one.features.stats.corebattery.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.BatteryManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Android Service that continuously monitors battery status changes and provides
 * raw battery data through CoreBatteryStatsProvider.
 * 
 * This service runs in the foreground to ensure reliability and registers
 * a BroadcastReceiver for ACTION_BATTERY_CHANGED intents.
 */
@AndroidEntryPoint
class CoreBatteryStatsService : Service() {
    

    
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    @Inject
    lateinit var appRepository: AppRepository

    private lateinit var batteryManager: BatteryManager
    private var batteryEventReceiver: BroadcastReceiver? = null

    // Service state tracking
    private var isForegroundServiceActive = false
    private var isRunningInFallbackMode = false
    private var foregroundStartupAttempts = 0

    // Cache for formatted notification content to reduce CPU usage
    private var lastFormattedContent: String? = null
    private var lastContentHash: Int = 0
    private var lastNotificationUpdateTime = 0L

    companion object {
        private const val TAG = "CoreBatteryStatsService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "unified_battery_monitor_channel"
        private const val CHANNEL_NAME = "Unified Battery Monitor"

        // Intent actions for controlling the service
        const val ACTION_START_MONITORING = "com.tqhit.battery.one.ACTION_START_CORE_BATTERY_MONITORING"
        const val ACTION_STOP_MONITORING = "com.tqhit.battery.one.ACTION_STOP_CORE_BATTERY_MONITORING"
        const val ACTION_SIMULATE_BATTERY_CHANGES = "com.tqhit.battery.one.ACTION_SIMULATE_BATTERY_CHANGES"

        // Notification update interval for real-time feel (5 seconds)
        private const val NOTIFICATION_UPDATE_INTERVAL_MS = 5000L

        // Optimization: Cache formatted strings to reduce CPU usage
        private const val DECIMAL_FORMAT_PATTERN = "%.1f"

        // Foreground service error handling constants
        private const val MAX_FOREGROUND_STARTUP_ATTEMPTS = 3
        private const val FOREGROUND_RETRY_DELAY_MS = 2000L

        // Static reference for debug access
        private var instance: CoreBatteryStatsService? = null

        fun getInstance(): CoreBatteryStatsService? = instance
    }
    
    override fun onCreate() {
        val serviceCreateStartTime = System.currentTimeMillis()
        val currentThread = Thread.currentThread()
        BatteryLogger.d(TAG, "STARTUP_TIMING: CoreBatteryStatsService.onCreate() started at $serviceCreateStartTime on thread: ${currentThread.name} (ID: ${currentThread.id})")

        super.onCreate()
        BatteryLogger.d(TAG, "CoreBatteryStatsService created")

        // Set static instance for debug access
        instance = this

        // Initialize BatteryManager for getting battery properties
        val batteryManagerStartTime = System.currentTimeMillis()
        batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        BatteryLogger.d(TAG, "STARTUP_TIMING: BatteryManager initialization took ${System.currentTimeMillis() - batteryManagerStartTime}ms")

        // Create notification channel for Android O+
        val notificationChannelStartTime = System.currentTimeMillis()
        createNotificationChannel()
        BatteryLogger.d(TAG, "STARTUP_TIMING: Notification channel creation took ${System.currentTimeMillis() - notificationChannelStartTime}ms")

        val totalCreateDuration = System.currentTimeMillis() - serviceCreateStartTime
        BatteryLogger.d(TAG, "STARTUP_TIMING: CoreBatteryStatsService.onCreate() completed in ${totalCreateDuration}ms")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val onStartCommandTime = System.currentTimeMillis()
        val currentThread = Thread.currentThread()
        BatteryLogger.d(TAG, "STARTUP_TIMING: onStartCommand called at $onStartCommandTime on thread: ${currentThread.name} (ID: ${currentThread.id}) with action: ${intent?.action}")

        when (intent?.action) {
            ACTION_START_MONITORING -> {
                val monitoringStartTime = System.currentTimeMillis()
                BatteryLogger.d(TAG, "STARTUP_TIMING: Starting core battery monitoring at $monitoringStartTime")
                if (attemptForegroundServiceStart()) {
                    startMonitoring()
                } else {
                    BatteryLogger.w(TAG, "Foreground service start failed, continuing in fallback mode")
                    isRunningInFallbackMode = true
                    startMonitoring()
                }
            }
            ACTION_STOP_MONITORING -> {
                BatteryLogger.d(TAG, "Stopping core battery monitoring")
                stopMonitoringAndService()
            }
            ACTION_SIMULATE_BATTERY_CHANGES -> {
                BatteryLogger.d(TAG, "Triggering battery percentage simulation")
                simulateBatteryPercentageChanges()
            }
            else -> {
                BatteryLogger.d(TAG, "Starting with default action")
                if (attemptForegroundServiceStart()) {
                    startMonitoring()
                } else {
                    BatteryLogger.w(TAG, "Foreground service start failed, continuing in fallback mode")
                    isRunningInFallbackMode = true
                    startMonitoring()
                }
            }
        }

        // Return START_STICKY to restart the service if it gets killed
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        // This service doesn't support binding
        return null
    }
    
    override fun onDestroy() {
        BatteryLogger.d(TAG, "CoreBatteryStatsService destroyed")
        stopMonitoringAndService()

        // Clear static instance
        instance = null

        super.onDestroy()
    }
    
    /**
     * Checks if the battery event receiver is currently registered.
     *
     * @return true if receiver is active, false otherwise
     */
    fun isReceiverRegistered(): Boolean {
        return batteryEventReceiver != null
    }

    /**
     * Attempts to start the service as a foreground service with comprehensive error handling.
     * Handles Android API level restrictions and permission requirements.
     *
     * @return true if successful, false if fallback mode should be used
     */
    private fun attemptForegroundServiceStart(): Boolean {
        BatteryLogger.d(TAG, "Attempting to start foreground service (Android ${Build.VERSION.SDK_INT}, attempt ${foregroundStartupAttempts + 1})")

        // Check if already running as foreground service
        if (isForegroundServiceActive) {
            BatteryLogger.d(TAG, "Already running as foreground service")
            return true
        }

        // Check notification permission for Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                BatteryLogger.w(TAG, "POST_NOTIFICATIONS permission not granted, cannot start foreground service")
                return false
            }
        }

        foregroundStartupAttempts++

        return try {
            val notification = createServiceNotification(null)
            startForeground(NOTIFICATION_ID, notification)
            isForegroundServiceActive = true
            BatteryLogger.d(TAG, "Successfully started as foreground service")
            true
        } catch (e: Exception) {
            handleForegroundServiceFailure(e)
            false
        }
    }

    /**
     * Handles foreground service startup failures with detailed logging and retry logic.
     *
     * @param exception The exception that occurred during foreground service startup
     */
    private fun handleForegroundServiceFailure(exception: Exception) {
        val errorMessage = when {
            exception.message?.contains("ForegroundServiceStartNotAllowedException") == true -> {
                "ForegroundServiceStartNotAllowedException: App not in foreground or user-visible state"
            }
            exception.message?.contains("SecurityException") == true -> {
                "SecurityException: Missing required permissions for foreground service"
            }
            exception.message?.contains("IllegalStateException") == true -> {
                "IllegalStateException: Service in invalid state for foreground startup"
            }
            else -> {
                "Unknown foreground service startup error: ${exception.message}"
            }
        }

        BatteryLogger.e(TAG, "Foreground service startup failed (attempt $foregroundStartupAttempts/$MAX_FOREGROUND_STARTUP_ATTEMPTS): $errorMessage", exception)

        // Log additional context for debugging
        BatteryLogger.d(TAG, "Service startup context - API: ${Build.VERSION.SDK_INT}, " +
                "Foreground active: $isForegroundServiceActive, " +
                "Fallback mode: $isRunningInFallbackMode")

        // Attempt retry with delay if under max attempts
        if (foregroundStartupAttempts < MAX_FOREGROUND_STARTUP_ATTEMPTS) {
            BatteryLogger.d(TAG, "Scheduling foreground service retry in ${FOREGROUND_RETRY_DELAY_MS}ms")

            // Use coroutine for delayed retry
            CoroutineScope(Dispatchers.Main).launch {
                delay(FOREGROUND_RETRY_DELAY_MS)
                if (!isForegroundServiceActive && !isRunningInFallbackMode) {
                    BatteryLogger.d(TAG, "Retrying foreground service startup")
                    attemptForegroundServiceStart()
                }
            }
        } else {
            BatteryLogger.w(TAG, "Max foreground startup attempts reached, service will run in fallback mode")
        }
    }
    
    /**
     * Creates the notification channel for unified battery monitoring on Android O+.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Unified battery monitoring with charging speed, temperature, percentage, and time estimation"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            BatteryLogger.d(TAG, "Unified notification channel created: $CHANNEL_ID")
        }
    }
    
    /**
     * Creates the unified foreground service notification with comprehensive battery information.
     * Includes charging speed, temperature, battery percentage, charging status, and smart time estimation.
     *
     * @param status Current battery status to display in notification (optional)
     * @return Notification object for the foreground service
     */
    private fun createServiceNotification(status: CoreBatteryStatus?): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // Always try to get current status if none provided
        val currentStatus = status ?: coreBatteryStatsProvider.getCurrentStatus()

        val contentText = if (currentStatus != null) {
            getFormattedUnifiedNotificationContent(currentStatus)
        } else {
            "Initializing battery monitoring..."
        }

        BatteryLogger.d(TAG, "Creating notification with content: $contentText")

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Unified Battery Monitor")
            .setContentText(contentText)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * Formats unified notification content with comprehensive battery information.
     * Includes caching to reduce CPU usage and smart time estimation.
     */
    private fun getFormattedUnifiedNotificationContent(status: CoreBatteryStatus): String {
        // Create a hash of the current status for cache comparison
        val currentHash = status.hashCode()

        // Return cached content if status hasn't changed
        if (currentHash == lastContentHash && lastFormattedContent != null) {
            return lastFormattedContent!!
        }

        // Calculate metrics using optimized approach
        val amperage = status.currentMicroAmperes / 1000.0f
        val power = calculatePowerWatts(status.currentMicroAmperes, status.voltageMillivolts)
        val temperature = status.temperatureCelsius

        // Format unified content with comprehensive battery information
        val content = buildString {
            // Battery percentage and charging status
            append("${status.percentage}% • ")
            append(if (status.isCharging) "Charging" else "Discharging")
            append(" • ")

            // Current charging speed
            append(String.format(DECIMAL_FORMAT_PATTERN, kotlin.math.abs(amperage)))
            append(" mA • ")

            // Temperature
            append(String.format(DECIMAL_FORMAT_PATTERN, temperature))
            append("°C")

            // Smart time estimation
            val timeEstimation = calculateTimeEstimation(status, amperage)
            if (timeEstimation.isNotEmpty()) {
                append(" • ")
                append(timeEstimation)
            }
        }

        // Cache the result
        lastFormattedContent = content
        lastContentHash = currentHash

        BatteryLogger.v(TAG, "Generated unified notification content: $content")
        return content
    }
    
    /**
     * Optimized power calculation using the standard formula: Power = (Voltage × Current) / 1,000,000
     * Extracted to a separate method for reusability and testing.
     */
    private fun calculatePowerWatts(currentMicroAmperes: Long, voltageMillivolts: Int): Float {
        return (currentMicroAmperes / 1000.0f) * (voltageMillivolts / 1000.0f) / 1000.0f
    }

    /**
     * Calculates smart time estimation based on current charging state and target percentage.
     * Returns time to target percentage or time to full charge.
     */
    private fun calculateTimeEstimation(status: CoreBatteryStatus, currentMa: Float): String {
        if (!status.isCharging || currentMa <= 0) {
            return "" // No estimation for discharging or invalid current
        }

        val currentPercentage = status.percentage
        val targetPercentage = appRepository.getChargeAlarmPercent()

        // Determine target: user-set target or 100% if already reached target
        val finalTarget = if (targetPercentage > 0 && currentPercentage < targetPercentage) {
            targetPercentage
        } else {
            100 // Time to full charge
        }

        if (currentPercentage >= finalTarget) {
            return "" // Already at or above target
        }

        // Estimate time based on current charging rate
        // Assume average battery capacity of 4000mAh (can be made configurable)
        val estimatedBatteryCapacityMah = 4000f
        val percentageToCharge = finalTarget - currentPercentage
        val energyNeededMah = (percentageToCharge / 100f) * estimatedBatteryCapacityMah

        // Calculate time in hours, accounting for charging efficiency (assume 85%)
        val chargingEfficiency = 0.85f
        val timeHours = energyNeededMah / (currentMa * chargingEfficiency)

        return formatTimeEstimation(timeHours, finalTarget)
    }

    /**
     * Formats time estimation into human-readable format.
     */
    private fun formatTimeEstimation(timeHours: Float, targetPercentage: Int): String {
        if (timeHours <= 0 || timeHours > 24) {
            return "" // Invalid or unrealistic time
        }

        val hours = timeHours.toInt()
        val minutes = ((timeHours - hours) * 60).toInt()

        val targetText = if (targetPercentage == 100) "full" else "${targetPercentage}%"

        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}m to $targetText"
            hours > 0 -> "${hours}h to $targetText"
            minutes > 0 -> "${minutes}m to $targetText"
            else -> "< 1m to $targetText"
        }
    }

    /**
     * Updates the foreground notification with current battery status.
     * Implements 5-second refresh rate for real-time updates, but allows immediate updates for initial data.
     * Only updates if running as foreground service.
     *
     * @param status Current battery status to display
     * @param forceUpdate Force immediate update regardless of timing
     */
    private fun updateForegroundNotification(status: CoreBatteryStatus, forceUpdate: Boolean = false) {
        // Only update notification if running as foreground service
        if (!isForegroundServiceActive) {
            BatteryLogger.v(TAG, "Skipping notification update - not running as foreground service")
            return
        }

        val now = System.currentTimeMillis()

        // Update notification every 5 seconds for real-time feel, or force update
        if (forceUpdate || now - lastNotificationUpdateTime >= NOTIFICATION_UPDATE_INTERVAL_MS) {
            try {
                val notification = createServiceNotification(status)
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.notify(NOTIFICATION_ID, notification)
                lastNotificationUpdateTime = now

                BatteryLogger.v(TAG, "Notification updated (forced: $forceUpdate, interval: ${now - lastNotificationUpdateTime}ms)")
            } catch (e: Exception) {
                BatteryLogger.w(TAG, "Failed to update notification", e)
            }
        }
    }

    /**
     * Starts monitoring battery status by registering a BroadcastReceiver
     * for ACTION_BATTERY_CHANGED and emits the initial battery status.
     */
    private fun startMonitoring() {
        if (batteryEventReceiver != null) {
            BatteryLogger.w(TAG, "Battery monitoring already started")
            return
        }

        BatteryLogger.d(TAG, "Starting battery status monitoring")

        // Create and register the broadcast receiver
        batteryEventReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == Intent.ACTION_BATTERY_CHANGED) {
                    BatteryLogger.v(TAG, "Battery status changed - processing intent")

                    try {
                        val newStatus = extractBatteryStatus(intent)

                        // Enhanced logging for health fragment debugging
                        BatteryLogger.d(TAG, "BATTERY_UPDATE: === NEW BATTERY STATUS DETECTED ===")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE: Status details:")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE:   Percentage: ${newStatus.percentage}%")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE:   Charging: ${newStatus.isCharging}")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE:   Current: ${newStatus.currentMicroAmperes}µA")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE:   Temperature: ${newStatus.temperatureCelsius}°C")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(newStatus.timestampEpochMillis))}")

                        coreBatteryStatsProvider.updateStatus(newStatus)

                        // Update the notification with new status (force update for real-time data)
                        updateForegroundNotification(newStatus, forceUpdate = true)

                        BatteryLogger.d(TAG, "BATTERY_UPDATE: ✅ Status updated and emitted to all observers")
                        BatteryLogger.d(TAG, "BATTERY_UPDATE: This will trigger health recalculation in HealthRepository")
                        BatteryLogger.d(TAG, "Battery status updated and emitted: $newStatus")
                    } catch (e: Exception) {
                        BatteryLogger.e(TAG, "BATTERY_UPDATE: ❌ Error processing battery status change", e)

                        // Emit default status on error to maintain data flow
                        val defaultStatus = createDefaultStatus()
                        coreBatteryStatsProvider.updateStatus(defaultStatus)
                        BatteryLogger.w(TAG, "BATTERY_UPDATE: Emitted default status to maintain data flow")
                    }
                }
            }
        }

        // Register the receiver for battery change events
        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        registerReceiver(batteryEventReceiver, filter)

        BatteryLogger.d(TAG, "Battery event receiver registered")

        // Emit initial battery status immediately
        try {
            val initialStatus = getInitialBatteryStatus()
            coreBatteryStatsProvider.updateStatus(initialStatus)
            updateForegroundNotification(initialStatus, forceUpdate = true)
            BatteryLogger.d(TAG, "Initial battery status emitted: $initialStatus")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting initial battery status", e)
            val defaultStatus = createDefaultStatus()
            coreBatteryStatsProvider.updateStatus(defaultStatus)
            updateForegroundNotification(defaultStatus, forceUpdate = true)
        }
    }

    /**
     * Stops monitoring and shuts down the service.
     * Unregisters the broadcast receiver and stops the foreground service.
     */
    private fun stopMonitoringAndService() {
        BatteryLogger.d(TAG, "Stopping battery monitoring and service")

        // Unregister the broadcast receiver if it exists
        batteryEventReceiver?.let { receiver ->
            try {
                unregisterReceiver(receiver)
                BatteryLogger.d(TAG, "Battery event receiver unregistered")
            } catch (e: IllegalArgumentException) {
                BatteryLogger.w(TAG, "Receiver was not registered", e)
            }
            batteryEventReceiver = null
        }

        // Stop foreground service if it was active
        if (isForegroundServiceActive) {
            try {
                stopForeground(STOP_FOREGROUND_REMOVE)
                BatteryLogger.d(TAG, "Foreground service stopped")
            } catch (e: Exception) {
                BatteryLogger.w(TAG, "Error stopping foreground service", e)
            }
            isForegroundServiceActive = false
        }

        // Reset state tracking
        isRunningInFallbackMode = false
        foregroundStartupAttempts = 0

        // Stop the service
        stopSelf()

        BatteryLogger.d(TAG, "CoreBatteryStatsService stopped")
    }

    /**
     * Checks if the service can start as a foreground service based on current conditions.
     *
     * @return true if foreground service startup is likely to succeed
     */
    private fun canStartForegroundService(): Boolean {
        // Check notification permission for Android 13+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }

        // Check if we've exceeded max attempts
        if (foregroundStartupAttempts >= MAX_FOREGROUND_STARTUP_ATTEMPTS) {
            return false
        }

        return true
    }

    /**
     * Gets the current service state for debugging and monitoring.
     *
     * @return Map containing current service state information
     */
    fun getServiceState(): Map<String, Any> {
        return mapOf(
            "isForegroundServiceActive" to isForegroundServiceActive,
            "isRunningInFallbackMode" to isRunningInFallbackMode,
            "foregroundStartupAttempts" to foregroundStartupAttempts,
            "isReceiverRegistered" to isReceiverRegistered(),
            "androidApiLevel" to Build.VERSION.SDK_INT
        )
    }

    /**
     * Attempts to recover from foreground service failures by trying alternative approaches.
     * This method can be called when the service needs to continue operating despite
     * foreground service restrictions.
     */
    fun attemptServiceRecovery() {
        BatteryLogger.d(TAG, "Attempting service recovery")

        if (isForegroundServiceActive) {
            BatteryLogger.d(TAG, "Service already running as foreground, no recovery needed")
            return
        }

        if (isRunningInFallbackMode) {
            BatteryLogger.d(TAG, "Already running in fallback mode")
            return
        }

        // Reset attempt counter for recovery
        foregroundStartupAttempts = 0

        // Try to start as foreground service again
        if (canStartForegroundService() && attemptForegroundServiceStart()) {
            BatteryLogger.d(TAG, "Service recovery successful - now running as foreground service")
            isRunningInFallbackMode = false
        } else {
            BatteryLogger.w(TAG, "Service recovery failed - continuing in fallback mode")
            isRunningInFallbackMode = true

            // Ensure monitoring is still active even in fallback mode
            if (!isReceiverRegistered()) {
                BatteryLogger.d(TAG, "Restarting monitoring in fallback mode")
                startMonitoring()
            }
        }
    }

    /**
     * Provides user-friendly status information about the service state.
     *
     * @return Human-readable status message
     */
    fun getServiceStatusMessage(): String {
        return when {
            isForegroundServiceActive -> "Battery monitoring active with notifications"
            isRunningInFallbackMode -> "Battery monitoring active (limited functionality)"
            isReceiverRegistered() -> "Battery monitoring starting..."
            else -> "Battery monitoring inactive"
        }
    }

    /**
     * Checks if the service is providing core functionality regardless of foreground status.
     *
     * @return true if battery monitoring is active
     */
    fun isMonitoringActive(): Boolean {
        return isReceiverRegistered()
    }

    /**
     * Retrieves the current battery status for immediate use.
     * This method queries the system directly to get the latest battery information.
     *
     * @return Current CoreBatteryStatus
     */
    private fun getInitialBatteryStatus(): CoreBatteryStatus {
        BatteryLogger.d(TAG, "Getting initial battery status")

        // Get current battery status using registerReceiver with null receiver
        val batteryIntent = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

        return if (batteryIntent != null) {
            extractBatteryStatus(batteryIntent)
        } else {
            BatteryLogger.w(TAG, "Could not get battery intent, using default status")
            createDefaultStatus()
        }
    }

    /**
     * Extracts battery status information from an ACTION_BATTERY_CHANGED Intent.
     * Parses all relevant battery data and creates a CoreBatteryStatus object.
     *
     * @param intent The ACTION_BATTERY_CHANGED intent containing battery data
     * @return CoreBatteryStatus object with extracted data
     */
    private fun extractBatteryStatus(intent: Intent): CoreBatteryStatus {
        BatteryLogger.v(TAG, "Extracting battery status from intent")

        // Extract basic battery information
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        val percentage = if (level >= 0 && scale > 0) {
            (level * 100 / scale).coerceIn(0, 100)
        } else {
            0
        }

        // Extract charging status
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
        val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                        status == BatteryManager.BATTERY_STATUS_FULL

        // Extract plugged source
        val pluggedSource = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

        // Extract voltage (in millivolts)
        val voltageMillivolts = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0)

        // Extract temperature (in tenths of degrees Celsius, convert to Celsius)
        val temperatureTenths = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
        val temperatureCelsius = temperatureTenths / 10.0f

        // Get current from BatteryManager (in microamperes)
        val currentMicroAmperes = try {
            val rawCurrentValue = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            // Check if value is small, assume it's mA and convert to µA
            if (kotlin.math.abs(rawCurrentValue) < 5000) {
                BatteryLogger.d(TAG, "Small current value detected: $rawCurrentValue mA, converting to µA")
                rawCurrentValue * 1000
            } else {
                rawCurrentValue
            }
        } catch (e: Exception) {
            BatteryLogger.w(TAG, "Could not get current from BatteryManager", e)
            0L
        }

        val timestamp = System.currentTimeMillis()

        val extractedStatus = CoreBatteryStatus(
            percentage = percentage,
            isCharging = isCharging,
            pluggedSource = pluggedSource,
            currentMicroAmperes = currentMicroAmperes,
            voltageMillivolts = voltageMillivolts,
            temperatureCelsius = temperatureCelsius,
            timestampEpochMillis = timestamp
        )

        BatteryLogger.d(TAG, "Extracted battery status: " +
            "level=$level/$scale ($percentage%), " +
            "status=$status (charging=$isCharging), " +
            "plugged=$pluggedSource, " +
            "voltage=${voltageMillivolts}mV, " +
            "temp=${temperatureCelsius}°C, " +
            "current=${currentMicroAmperes}µA")

        return extractedStatus
    }

    /**
     * Creates a default CoreBatteryStatus when real data is unavailable.
     * This ensures the application always has a valid status to work with.
     *
     * @return Default CoreBatteryStatus with safe fallback values
     */
    private fun createDefaultStatus(): CoreBatteryStatus {
        BatteryLogger.w(TAG, "Creating default battery status")
        return CoreBatteryStatus.createDefault()
    }

    /**
     * Simulates battery percentage changes for testing real-time data collection.
     * This method injects simulated battery status updates to test the complete data pipeline.
     */
    fun simulateBatteryPercentageChanges() {
        BatteryLogger.d(TAG, "BATTERY_SIMULATION: === STARTING BATTERY PERCENTAGE SIMULATION ===")

        // Define realistic battery percentage changes
        val percentageChanges = listOf(100, 97, 94, 91)
        val intervalSeconds = 60L

        BatteryLogger.d(TAG, "BATTERY_SIMULATION: Will simulate ${percentageChanges.size} percentage changes")
        BatteryLogger.d(TAG, "BATTERY_SIMULATION: Interval: ${intervalSeconds}s between changes")
        BatteryLogger.d(TAG, "BATTERY_SIMULATION: Sequence: ${percentageChanges.joinToString(" → ")}%")

        // Use a coroutine to simulate changes with proper timing
        CoroutineScope(Dispatchers.Main).launch {
            percentageChanges.forEachIndexed { index, percentage ->
                BatteryLogger.d(TAG, "BATTERY_SIMULATION: === SIMULATING CHANGE ${index + 1}/${percentageChanges.size} ===")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION: Setting battery percentage to $percentage%")

                // Create simulated battery status using CoreBatteryStatus
                val simulatedStatus = CoreBatteryStatus(
                    percentage = percentage,
                    isCharging = false, // Simulate discharge
                    pluggedSource = 0, // Unplugged
                    currentMicroAmperes = -800L - (kotlin.random.Random.nextInt(400).toLong()), // -800 to -1200 µA discharge
                    voltageMillivolts = 3800 + kotlin.random.Random.nextInt(400), // 3800-4200 mV
                    temperatureCelsius = (30.0 + kotlin.random.Random.nextDouble() * 5.0).toFloat(), // 30-35°C
                    timestampEpochMillis = System.currentTimeMillis()
                )

                BatteryLogger.d(TAG, "BATTERY_SIMULATION: Simulated status details:")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION:   Percentage: ${simulatedStatus.percentage}%")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION:   Charging: ${simulatedStatus.isCharging}")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION:   Current: ${simulatedStatus.currentMicroAmperes}µA")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION:   Temperature: ${simulatedStatus.temperatureCelsius}°C")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(simulatedStatus.timestampEpochMillis))}")

                // Inject the simulated status into the data pipeline
                coreBatteryStatsProvider.updateStatus(simulatedStatus)

                // Update notification to reflect simulated status
                updateForegroundNotification(simulatedStatus, forceUpdate = true)

                BatteryLogger.d(TAG, "BATTERY_SIMULATION: ✅ Simulated status injected into data pipeline")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION: This should trigger HistoryBatteryRepository data collection")
                BatteryLogger.d(TAG, "BATTERY_SIMULATION: This should trigger HealthFragment chart updates")

                // Wait for the specified interval before next change (except for last iteration)
                if (index < percentageChanges.size - 1) {
                    BatteryLogger.d(TAG, "BATTERY_SIMULATION: ⏳ Waiting ${intervalSeconds}s before next change...")
                    delay(intervalSeconds * 1000)
                }
            }

            BatteryLogger.d(TAG, "BATTERY_SIMULATION: === BATTERY PERCENTAGE SIMULATION COMPLETED ===")
            BatteryLogger.d(TAG, "BATTERY_SIMULATION: All ${percentageChanges.size} percentage changes have been simulated")
            BatteryLogger.d(TAG, "BATTERY_SIMULATION: Check HistoryBatteryRepository and HealthFragment logs for data collection")
        }
    }
}
