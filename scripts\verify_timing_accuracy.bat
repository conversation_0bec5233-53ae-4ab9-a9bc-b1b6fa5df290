@echo off
REM TJ_BatteryOne Timing Accuracy Verification Script
REM This script runs multiple cold start tests to verify timing log accuracy

setlocal enabledelayedexpansion

set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set MAIN_ACTIVITY=%APP_ID%/com.tqhit.battery.one.activity.splash.SplashActivity
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set LOG_FILE=timing_verification_%TIMESTAMP%.log

echo ========================================
echo TJ_BatteryOne Timing Accuracy Verification
echo ========================================
echo Application ID: %APP_ID%
echo Log File: %LOG_FILE%
echo Timestamp: %TIMESTAMP%
echo ========================================

REM Create log file header
echo Timing Accuracy Verification - %TIMESTAMP% > %LOG_FILE%
echo Application ID: %APP_ID% >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

echo.
echo Running 3 cold start tests to verify timing accuracy...
echo.

REM Perform multiple cold start measurements
for /L %%i in (1,1,3) do (
    echo === Cold Start Test %%i/3 ===
    echo Cold Start Test %%i/3 >> %LOG_FILE%
    
    REM Force stop app
    adb shell am force-stop %APP_ID%
    timeout /t 3 /nobreak > nul
    
    REM Clear logcat
    adb logcat -c
    
    REM Start logcat monitoring in background
    start /b adb logcat -s STARTUP_TIMING:D MAX_INIT:D UI_TIMING:D CoreBatteryStatsService:D BatteryApplication:D SplashActivity:D MainActivity:D > logcat_test_%%i_%TIMESTAMP%.log
    
    REM Wait for logcat to initialize
    timeout /t 1 /nobreak > nul
    
    REM Measure cold start time
    echo Launching app and measuring startup time...
    adb shell am start -W -n %MAIN_ACTIVITY% >> %LOG_FILE% 2>&1
    
    REM Wait for app to fully load and logs to be captured
    timeout /t 15 /nobreak > nul
    
    REM Stop logcat monitoring
    taskkill /f /im adb.exe > nul 2>&1
    timeout /t 2 /nobreak > nul
    
    echo Test %%i completed.
    echo ---------------------------------------- >> %LOG_FILE%
    echo. >> %LOG_FILE%
)

echo.
echo Analyzing timing logs for accuracy verification...
echo ======================================== >> %LOG_FILE%
echo TIMING ANALYSIS >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

REM Analyze each test's timing logs
for /L %%i in (1,1,3) do (
    echo. >> %LOG_FILE%
    echo === Test %%i Timing Analysis === >> %LOG_FILE%
    
    if exist logcat_test_%%i_%TIMESTAMP%.log (
        echo BatteryApplication Timings: >> %LOG_FILE%
        findstr /C:"BatteryApplication" logcat_test_%%i_%TIMESTAMP%.log | findstr /C:"STARTUP_TIMING" >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo SplashActivity Timings: >> %LOG_FILE%
        findstr /C:"SplashActivity" logcat_test_%%i_%TIMESTAMP%.log | findstr /C:"UI_TIMING" >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo MainActivity Timings: >> %LOG_FILE%
        findstr /C:"MainActivity" logcat_test_%%i_%TIMESTAMP%.log | findstr /C:"UI_TIMING" >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo MAX SDK Timings: >> %LOG_FILE%
        findstr /C:"MAX_INIT" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo CoreBatteryStatsService Timings: >> %LOG_FILE%
        findstr /C:"CoreBatteryStatsService" logcat_test_%%i_%TIMESTAMP%.log | findstr /C:"STARTUP_TIMING" >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo Memory Usage Logs: >> %LOG_FILE%
        findstr /C:"Memory usage" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
        
        echo. >> %LOG_FILE%
        echo Thread Information: >> %LOG_FILE%
        findstr /C:"thread:" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
        
    ) else (
        echo WARNING: Logcat file for test %%i not found >> %LOG_FILE%
    )
    
    echo ---------------------------------------- >> %LOG_FILE%
)

echo.
echo Generating timing accuracy summary...
echo ======================================== >> %LOG_FILE%
echo TIMING ACCURACY SUMMARY >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

REM Extract key timing metrics
echo Key Timing Metrics Across All Tests: >> %LOG_FILE%
echo. >> %LOG_FILE%

echo BatteryApplication onCreate Timings: >> %LOG_FILE%
for /L %%i in (1,1,3) do (
    if exist logcat_test_%%i_%TIMESTAMP%.log (
        echo Test %%i: >> %LOG_FILE%
        findstr /C:"BatteryApplication.onCreate() completed" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
    )
)

echo. >> %LOG_FILE%
echo SplashActivity onCreate Timings: >> %LOG_FILE%
for /L %%i in (1,1,3) do (
    if exist logcat_test_%%i_%TIMESTAMP%.log (
        echo Test %%i: >> %LOG_FILE%
        findstr /C:"SplashActivity.onCreate() completed" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
    )
)

echo. >> %LOG_FILE%
echo MAX SDK Initialization Timings: >> %LOG_FILE%
for /L %%i in (1,1,3) do (
    if exist logcat_test_%%i_%TIMESTAMP%.log (
        echo Test %%i: >> %LOG_FILE%
        findstr /C:"MAX SDK initialization completed" logcat_test_%%i_%TIMESTAMP%.log >> %LOG_FILE%
    )
)

echo.
echo ========================================
echo VERIFICATION COMPLETED
echo ========================================
echo Results saved to: %LOG_FILE%
echo Individual test logs:
for /L %%i in (1,1,3) do (
    echo - logcat_test_%%i_%TIMESTAMP%.log
)
echo.
echo Check %LOG_FILE% for detailed timing analysis.
echo Compare ADB cold start times with internal timing logs.
echo.
pause
